#![feature(
    closure_lifetime_binder,
    coroutines,
    coroutine_trait,
    iterator_try_collect,
    // generic_const_exprs,
    // generic_const_items,
    // non_lifetime_binders,
    step_trait,
    stmt_expr_attributes,
    thread_id_value,
    try_blocks,
    type_alias_impl_trait,
)]

use std::{
    collections::{
        BTreeMap,
        HashSet,
    },
    fmt::Display,
    ops::Deref,
    sync::{
        Arc,
        LazyLock,
    },
};

use async_recursion::async_recursion;
use futures::future::FutureExt;
use hashlink::LinkedHashMap;
use headless_chrome::{
    B<PERSON><PERSON>,
    Element as DynamicElement,
    Tab,
};
use polars::prelude::IntoVec;
use regex::Regex;
// use polars::prelude::*;
use scraper::{
    ElementRef as StaticElement,
    Html,
    Selector,
};
// use serde::Serialize;
use slog::{
    Drain,
    debug,
    error,
    info,
};
use thiserror::Error;
use tokio::{
    task::JoinSet,
    time::Instant,
};
use yaml_rust2::{
    Yaml,
    Yaml<PERSON><PERSON><PERSON>,
    Yam<PERSON><PERSON>oader,
};


/// Global logger
static LOG: LazyLock<slog::Logger> = LazyLock::new(|| {
    let decorator = slog_term::PlainSyncDecorator::new(std::io::stdout());
    slog::Logger::root(slog_term::FullFormat::new(decorator).build().fuse(), slog::o!())
});
static DRIVER: LazyLock<Arc<Browser>> =
    LazyLock::new(|| Arc::new(Browser::default().expect("Failed to connect to browser")));
const AKC_BREEDS_PAGES: u16 = 25;
const PURINA_BREEDS_PAGES: u16 = 20;
static REGEX_SPACES: LazyLock<Regex> = LazyLock::new(|| Regex::new(r"\s+").unwrap());
static REGEX_EMPTY_LINES: LazyLock<Regex> = LazyLock::new(|| Regex::new(r"\n+").unwrap());


// * Models

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
enum Keyword {
    Attribute,
    Dynamic,
    Follow,
    Key,
    Kind,
    Selector,
    Static,
    Title,
    Type,
    Url,
    Value,
    Variants,
}

/// Implement transparent use as &str
impl Deref for Keyword {
    type Target = str;

    fn deref(&self) -> &Self::Target {
        match self {
            Self::Attribute => "attribute",
            Self::Dynamic => "dynamic",
            Self::Follow => "follow",
            Self::Key => "key",
            Self::Kind => "kind",
            Self::Selector => "selector",
            Self::Static => "static",
            Self::Title => "title",
            Self::Type => "type",
            Self::Url => "url",
            Self::Value => "value",
            Self::Variants => "variants",
        }
    }
}

#[derive(Debug, Clone, PartialEq)]
enum ScrappedData {
    Vec(Vec<Self>),
    Map(LinkedHashMap<String, Self>),
    String(String),
    Float(f32),
    Int(i32),
    None,
}
impl Display for ScrappedData {
    fn fmt(&self, fmt: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::Vec(vec) => {
                write!(fmt, "[")?;
                write!(
                    fmt,
                    "{}",
                    vec.iter()
                        .map(|item| format!("{item}"))
                        .collect::<Vec<_>>()
                        .join(", ")
                )?;
                write!(fmt, "]")
            },
            Self::Map(map) => {
                write!(fmt, "{{")?;
                write!(
                    fmt,
                    "{}",
                    map.iter()
                        .map(|(k, v)| format!("\"{k}\": {v}"))
                        .collect::<Vec<_>>()
                        .join(", ")
                )?;
                write!(fmt, "}}")
            },
            Self::String(s) => write!(
                fmt,
                "\"{}\"",
                s.replace('\\', "\\\\")
                    .replace('\"', "\\\"")
                    .replace('\n', "\\n")
            ),
            Self::Float(f) => write!(fmt, "{f:.1}"),
            Self::Int(i) => write!(fmt, "{i}"),
            Self::None => write!(fmt, "undefined"),
        }
    }
}
// impl Deref for ScrappedData::Vec {
//     type Target = Vec<Self>;

//     fn deref(&self) -> &Self::Target {
//         match self {
//             Self::Vec(vec) => vec,
//             _ => panic!("Cannot deref non-Vec"),
//         }
//     }
// }
// impl Deref for ScrappedData {
//     type Target = LinkedHashMap<String, Self>;

//     fn deref(&self) -> &Self::Target {
//         match self {
//             Self::Map(map) => map,
//             _ => panic!("Cannot deref non-Map"),
//         }
//     }
// }
// impl AsRef<Vec<ScrappedData>> for ScrappedData {
impl AsRef<Vec<ScrappedData>> for ScrappedData {
    fn as_ref(&self) -> &Vec<ScrappedData> {
        match self {
            Self::Vec(vec) => vec,
            _ => panic!("Cannot deref non-Vec"),
        }
    }
}
impl AsRef<LinkedHashMap<String, ScrappedData>> for ScrappedData {
    fn as_ref(&self) -> &LinkedHashMap<String, ScrappedData> {
        match self {
            Self::Map(map) => map,
            _ => panic!("Cannot deref non-Map"),
        }
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
enum ValueType {
    DynamicMap,
    Float,
    Int,
    List,
    Map,
    Single,
    Quantity,
    String,
    Text,
    Url,
    Variants,
}
impl ValueType {
    fn from_str(s: &str) -> Self {
        match s.trim().to_lowercase().as_str() {
            "dynamic_map" => Self::DynamicMap,
            "float" => Self::Float,
            "int" => Self::Int,
            "list" => Self::List,
            // "map" => Self::Map,
            "quantity" => Self::Quantity,
            "text" => Self::Text,
            "url" => Self::Url,
            "variants" => Self::Variants,
            _ => Self::String,
        }
    }
}

// * Main

// #[tokio::main(worker_threads = 32)]
#[tokio::main]
async fn main() {
    info!(LOG, "Starting.\n");

    let yaml = std::fs::read_to_string("breeds.yaml").unwrap();
    let rules = YamlLoader::load_from_str(&yaml).unwrap();
    let rule = rules.first().unwrap();

    let now = Instant::now();
    let results = scrape_by_rules(rule).await;
    info!(LOG, "Scraped in {:.0?}", now.elapsed());
    debug!(LOG, "Results: {}", results);

    info!(LOG, "Done.");
}


// * Scrapers


#[derive(Debug, Error)]
pub enum GetError {
    #[error("Max retries exceeded.")]
    MaxRetries(#[from] ureq::Error),
}

fn get_html_page_content(url: &str) -> Result<String, GetError> {
    const MAX_RETRIES: usize = 3;

    let mut agent: ureq::Agent = ureq::Agent::config_builder()
        .timeout_per_call(Some(std::time::Duration::from_millis(3000)))
        .build()
        .into();
    let mut retries = 0;
    loop {
        match agent.get(url).call() {
            Ok(mut response) => match response.body_mut().read_to_string() {
                Ok(mut body) => {
                    return Ok(body);
                },
                Err(err) => {
                    if retries >= MAX_RETRIES {
                        return Err(GetError::MaxRetries(err));
                    }
                    retries += 1;
                },
            },
            Err(err) => {
                if retries >= MAX_RETRIES {
                    return Err(GetError::MaxRetries(err));
                }
                retries += 1;
            },
        }
    }
}

const RESERVED_KEYS: [&str; 11] = [
    "name",
    "url",
    "kind",
    "page",
    "selector",
    "key",
    "value",
    "value-options",
    "attribute",
    "follow",
    "prefix",
];
/// Function that recursively scrapes data from website(s) based on a set of rules.
/// The rules are defined in a YAML file.
/// Example of the rules could be found in the `breeds.yaml` file.
/// When a `children` key is present in the rule, the function is called recursively.
/// When `url` key is present in the rule and `follow` key is set to `true`, the function scrapes the data
/// from the website. When `key` and `value` keys are present in the rule, the function extracts
/// all key/value pairs from the HTML element that matches the selector.
/// If `attribute` key is present, the function extracts the value of the attribute.
/// If a placeholder is present in the `url` key (e.g. `{page}`), the function
/// scrapes the data from multiple pages by replacing the placeholder with the page number.
/// A single value, a set of values of a range would be provided in the YAML key
/// with a respective to a placeholder name.
/// Available data types defined in the `type` key: `string`, `int`, `float`,
/// `text`. `text` value corresponds to the multiline text and can be collected
/// from nested <p> tags or, if there are no <p> tags, from the `text()`.
/// All processing is done using multithreading with `spawn_blocking`.
/// `kind` key defines the type of the scraping: `static` or `dynamic`. `static`
/// scraping is done using `scrap_from_static_page` function for single pages  and
/// `scrap_list_from_static_pages` function for lists of pages, `dynamic` - using
/// `scrap_from_dynamic_page` function for single pages and
/// `scrap_list_from_dynamic_pages` function for lists of pages.
#[allow(clippy::too_many_lines)]
#[async_recursion]
async fn scrape_by_rules(rule: &Yaml) -> Arc<ScrappedData> {
    // * Here we process the top level of YAML, i.e. until we find a `url` key.

    // * If is a list of entries, scrape them in parallel
    if rule.is_array() {
        // debug!(LOG, "Scraping list of entries:");
        let mut rule_map = LinkedHashMap::new();
        for r in rule.as_vec().unwrap() {
            let key = r
                .as_hash()
                .unwrap()
                .keys()
                .next()
                .unwrap()
                .as_str()
                .unwrap()
                .trim()
                .to_lowercase();
            let key = REGEX_SPACES.replace_all(&key, "_").to_string();
            rule_map.insert(key.clone(), r[key.as_str()].clone());
        }


        let mut tasks = JoinSet::new();
        for r in rule_map.values().cloned() {
            tasks.spawn(async move { scrape_by_rules(&r).await });
        }
        let items = tasks.join_all().await;

        let mut results = LinkedHashMap::new();
        for (i, rule) in rule_map.iter().enumerate() {
            results.insert(rule.0.clone(), items[i].as_ref().clone());
        }

        return Arc::new(ScrappedData::Map(results));
    }

    // * If is a single entry, skip it, it is not going to be a rule.
    // * Also skip entries when `url` or `value` keys are not present.
    if !rule.is_hash()
        || rule[&*Keyword::Url].is_badvalue()
        || rule[&*Keyword::Value].is_badvalue() && rule[&*Keyword::Variants].is_badvalue()
    {
        return Arc::new(ScrappedData::None);
    }

    let url = rule[&*Keyword::Url].as_str().unwrap();

    // * Check if we have placeholders in the URL by extracting all text between `{` and `}`
    let placeholders: Vec<&str> = Regex::new(r"\{(.*?)\}")
        .unwrap()
        .captures(url)
        .map(|caps| {
            let count = caps.len() - 1;
            (0..count)
                .map(move |i| caps.get(i + 1).unwrap().as_str())
                .collect()
        })
        .unwrap_or_default();

    // * Get placeholder values from the rule, could be a set of values or a range
    let mut placeholder_values = placeholders
        .into_iter()
        .map(move |placeholder| {
            let placeholder_rule = &rule[placeholder];
            assert!(!placeholder_rule.is_badvalue(), "Placeholder {placeholder} has no value");
            if placeholder_rule.is_array() {
                return (
                    placeholder,
                    placeholder_rule
                        .as_vec()
                        .unwrap()
                        .iter()
                        .map(|v| v.as_str().unwrap().to_string())
                        .collect(),
                );
            } else if let Some(value) = placeholder_rule.as_str() {
                let values = value.split("..").collect::<Vec<&str>>();
                let start: u16 = values[0].parse().unwrap();
                let end: u16 = values[1].parse().unwrap();
                return (placeholder, (start..=end).map(|i| i.to_string()).collect());
            }
            panic!("Unknown placeholder value type for {placeholder}");
        })
        .collect::<LinkedHashMap<&str, Vec<String>>>();
    // debug!(LOG, "Placeholder values: {:?}", &placeholder_values);

    // * Get all possible URL combinations
    let mut urls = vec![url.to_string()];
    if !placeholder_values.is_empty() {
        for (placeholder, values) in placeholder_values {
            let mut new_urls = Vec::new();
            for value in values {
                for url in &urls {
                    new_urls.push(url.replace(&format!("{{{placeholder}}}"), &value));
                }
            }
            urls = new_urls;
        }
    }

    let is_dynamic = rule[&*Keyword::Kind].as_str().unwrap_or_default() == &*Keyword::Dynamic;
    let mut tasks = JoinSet::new();
    for url in urls {
        let rule = rule.clone();
        tasks.spawn_blocking(move || {
            debug!(LOG, "Scraping (root) {url}");
            let mut html_content: Option<String> = None;
            if is_dynamic {
                let tab = DRIVER.new_tab().unwrap();
                tab.navigate_to(&url).unwrap();
                tab.wait_until_navigated();
                html_content = Some(tab.get_content().unwrap());
                tab.close(false).unwrap();
            } else {
                html_content = Some(get_html_page_content(&url).unwrap());
            }
            if let Some(html_content) = html_content {
                extract_by_rules_static_from_html(&html_content, &rule, false)
            } else {
                panic!("Failed to get HTML for {url}");
            }
        });
    }
    let results = tasks.join_all().await;
    return Arc::new(ScrappedData::Vec(
        if !results.is_empty() && matches!(results[0], ScrappedData::Vec(_)) {
            results
                .into_iter()
                .filter_map(|r| match r {
                    ScrappedData::Vec(v) => Some(v),
                    _ => None,
                })
                .flatten()
                .collect()
        } else {
            results
        },
    ));

    let result = if rule[&*Keyword::Value].is_badvalue() {
        // TODO: Properly deal with variants
        scrape_by_rules(&rule[&*Keyword::Variants]).await
    } else {
        scrape_by_rules(&rule[&*Keyword::Value]).await
    };

    Arc::clone(&result)
}

#[allow(clippy::too_many_lines)]
fn extract_by_rules_static_from_html(html_content: &str, rule: &Yaml, debug: bool) -> ScrappedData {
    let html = Html::parse_document(html_content);
    extract_by_rules_static(html.root_element(), rule, debug)
}

#[allow(clippy::too_many_lines)]
fn extract_by_rules_static(parent: StaticElement, rule: &Yaml, debug: bool) -> ScrappedData {
    let is_map = rule.is_array();
    let has_nested_value = !rule[&*Keyword::Value].is_badvalue();
    let has_nested_value_variants = !rule[&*Keyword::Variants].is_badvalue();
    let has_value = has_nested_value || has_nested_value_variants;
    let element_type_str = rule[&*Keyword::Type].as_str().unwrap_or_default();
    let value_type = if is_map {
        ValueType::Map
    } else if !element_type_str.is_empty() {
        ValueType::from_str(element_type_str)
    } else if !rule[&*Keyword::Key].is_badvalue() {
        ValueType::DynamicMap
    } else if has_nested_value {
        ValueType::Single
    } else if has_nested_value_variants {
        ValueType::Variants
    } else {
        ValueType::String
    };
    let is_list = value_type == ValueType::List;

    let selector = rule[&*Keyword::Selector].as_str().unwrap_or_default();
    let elements = if selector.is_empty() {
        vec![parent]
    } else {
        let query = Selector::parse(selector).unwrap();
        parent.select(&query).collect()
    };
    let element = if selector.is_empty() {
        parent
    } else {
        let element = elements.first();
        if element.is_none() {
            return match value_type {
                ValueType::List => ScrappedData::Vec(vec![]),
                ValueType::Map => ScrappedData::Map(LinkedHashMap::new()),
                _ => ScrappedData::None,
            };
        }
        *element.unwrap()
    };
    if debug {
        debug!(
            LOG,
            "[{:?}] \"{}\" ({}): {:?}, children: {:?}\n{}\n\n",
            value_type,
            selector,
            elements.len(),
            element,
            element.child_elements().collect::<Vec<_>>(),
            element.html()
        );
    }

    let get_nested_value = |element: StaticElement| {
        if has_nested_value {
            return extract_by_rules_static(element, &rule[&*Keyword::Value], debug);
        }
        if has_nested_value_variants {
            for rule_variant in rule[&*Keyword::Variants].as_vec().unwrap() {
                if !rule_variant.is_hash() {
                    continue;
                }
                let result = extract_by_rules_static(
                    element,
                    rule_variant.as_hash().unwrap().values().next().unwrap(),
                    debug,
                );
                if !matches!(result, ScrappedData::None) {
                    return result;
                }
            }
        }
        ScrappedData::None
    };

    match value_type {
        ValueType::Single | ValueType::Variants => get_nested_value(element),
        ValueType::List => {
            let mut results = Vec::new();
            if debug {
                debug!(LOG, "Scraping list of entries ({}).\n", elements.len());
            }
            for element in elements {
                let item = get_nested_value(element);
                results.push(item);
            }
            // * If children are Map and all keys are unique, convert the Vec to a Map
            if results.iter().all(|r| matches!(r, ScrappedData::Map(_))) {
                let keys: Vec<&String> = results
                    .iter()
                    .flat_map(|r| r.as_ref().keys().collect::<Vec<_>>())
                    // .flat_map(|r| match r {
                    //     ScrappedData::Map(map) => map.keys().collect(),
                    //     _ => vec![],
                    // })
                    .collect();
                if keys.iter().collect::<HashSet<_>>().len() == keys.len() {
                    let mut map = LinkedHashMap::new();
                    for result in results {
                        if let ScrappedData::Map(item) = result {
                            map.extend(item);
                        }
                    }
                    return ScrappedData::Map(map);
                }
            }

            ScrappedData::Vec(results)
        },

        ValueType::Map => {
            let mut results = LinkedHashMap::new();
            for r in rule.as_vec().unwrap() {
                let key = r
                    .as_hash()
                    .unwrap()
                    .keys()
                    .next()
                    .unwrap()
                    .as_str()
                    .unwrap()
                    .to_string();
                // let debug = debug || key == "details";
                // let debug = debug || key == "basic_details";
                // let debug = debug || key == "description";
                let debug = debug || key == "advanced_details";
                // let debug = true;
                if debug {
                    debug!(LOG, "\n\n- '{key}': {:?}", r[key.as_str()].clone());
                }
                let value = extract_by_rules_static(element, &r[key.as_str()], debug);
                results.insert(key, value);
            }

            ScrappedData::Map(results)
        },
        ValueType::DynamicMap => {
            let key = element
                .select(&Selector::parse(&rule[&*Keyword::Key].as_str().unwrap()).unwrap())
                .next()
                .unwrap()
                .text()
                .next()
                .unwrap()
                .trim()
                .to_lowercase();
            let key = REGEX_SPACES.replace_all(&key, "_").to_string();
            let value = if has_value {
                get_nested_value(element)
            } else {
                ScrappedData::None
            };
            ScrappedData::Map(LinkedHashMap::from_iter([(key, value)]))
        },

        ValueType::Text => {
            let base_text = element.text().next().unwrap_or_default().trim().to_string();
            if debug {
                debug!(LOG, "base_text: {}", base_text);
            }
            let full_text = element
                .select(&Selector::parse("*").unwrap())
                .filter_map(|p| p.text().next().map(str::trim))
                .filter(|s| !s.is_empty())
                .collect::<Vec<_>>()
                .join("\n");
            if debug {
                debug!(LOG, "full_text: {}", full_text);
            }

            let mut text = if full_text.starts_with(base_text.as_str()) {
                full_text
            } else {
                format!("{base_text}\n{full_text}")
            };
            text = text.replace('\n', "{\\n}");
            text = REGEX_SPACES.replace_all(&text, " ").to_string();
            text = text.replace("{\\n}", "\n");

            ScrappedData::String(text)
        },

        ValueType::Url => {
            let attribute = rule[&*Keyword::Attribute].as_str().unwrap_or("href");
            let url = element
                .value()
                .attr(attribute)
                .unwrap_or_default()
                .trim()
                .to_lowercase();

            if has_value && url.starts_with("http") && rule[&*Keyword::Follow].as_bool().unwrap_or(false) {
                let rule = rule.clone();
                let is_dynamic = rule[&*Keyword::Kind].as_str().unwrap_or_default() == &*Keyword::Dynamic;
                futures::executor::block_on(async {
                    let url = url.clone();
                    tokio::task::spawn_blocking(move || {
                        debug!(LOG, "Scraping {url}");
                        let mut html_content = if is_dynamic {
                            let tab = DRIVER.new_tab().unwrap();
                            tab.navigate_to(&url).unwrap();
                            tab.wait_until_navigated();
                            let html_content = tab.get_content().unwrap();
                            // debug!(LOG, "Got content for {url}: {:?}", html_content);
                            tab.close(false).unwrap();
                            html_content
                        } else {
                            get_html_page_content(&url).unwrap()
                        };
                        // TODO: Deal with variants
                        extract_by_rules_static_from_html(&html_content, &rule[&*Keyword::Value], debug)
                    })
                    .await
                    .unwrap()
                })
            } else {
                ScrappedData::String(url)
            }
        },

        ValueType::Int => {
            let text = element.text().next().unwrap_or_default().trim().to_string();
            match text.parse::<i32>() {
                Ok(i) => ScrappedData::Int(i),
                Err(_) => ScrappedData::None,
            }
        },

        ValueType::Float => {
            let text = element.text().next().unwrap_or_default().trim().to_string();
            match text.parse::<f32>() {
                Ok(f) => ScrappedData::Float(f),
                Err(_) => ScrappedData::None,
            }
        },

        ValueType::String => {
            let text = element.text().next().unwrap_or_default().trim().to_string();
            // if debug {
            //     debug!(LOG, "value: {}", text);
            // }
            ScrappedData::String(text)
        },

        // * Quantity represents a number of children found with a given selector.
        ValueType::Quantity =>
            if elements.is_empty() {
                ScrappedData::None
            } else {
                ScrappedData::Int(elements.len() as i32)
            },

        _ => ScrappedData::None,
    }
}
