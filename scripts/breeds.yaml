# yaml-language-server: $schema=scrapper-schema.json
- akc:
    title: AKC
    url: https://www.akc.org/dog-breeds/page/{page}/
    kind: static
    # page: 1..25
    page: 1..1
    # type: list
    selector: .breed-type-card
    value:
        # - name:
        #     selector: h3
        - details:
            selector: a
            attribute: href
            type: url
            follow: true
            kind: dynamic
            value:
                - description:
                    selector: .breed-page__about__read-more__text__less
                    type: text # Use <p> if not empty, otherwise use text()
                - basic_details:
                    selector: .breed-page__hero__overview__icon-block-wrap > .breed-page__hero__overview__icon-block
                    type: list
                    value:
                        key: h3
                        value:
                            selector: p
                            type: float | string
                - advanced_details:
                    # selector: "#breed-page__traits__all > .breed-trait-group > .breed-trait-group__column"
                    selector: .breed-trait-group__trait-all
                    type: list
                    value:
                        key: h4.breed-trait-group__header
                        variants:
                            - 1:
                                selector: .breed-trait-score__choice--selected > span
                                type: string
                            - 2:
                                selector: .breed-trait-score__score-unit--filled
                                type: quantity
                # - list_example:
                #     selector: .breed-page__traits_list
                #     type: list
                #     value:
                #         type: string
                #         selector: .breed-page__traits_list__item > p
        # - image:
        #     selector: img
        #     attribute: data-src
        #     type: url
# - purina:
#     title: Purina
#     url: https://www.purina.co.uk/find-a-pet/dog-breeds?page=%2C{page}
#     kind: static
#     page: 0..19
#     selector: .result-animal-container
#     type: list
#     children:
#         - name:
#             selector: h4 > a
#             type: string
#         - details:
#             selector: a
#             attribute: href
#             type: url
#             kind: static
#             prefix: https://www.purina.co.uk
#             follow: true
#             children:
#                 - description:
#                     selector: .field--name-field-nppe-bs-description
#                     type: text
#                 - basic_details:
#                     selector: .field--name-field-key-facts
#                     type: text
#                 - advanced_details:
#                     selector: .field--name-field-c-subitems
#                     type: text
#         - image:
#             selector: img
#             attribute: src
#             type: url
#             prefix: https://www.purina.co.uk
